/* Modern APEX Portfolio Styles */

/*****************************************
Modern Color Palette:
- Primary: #6366f1 (Indigo)
- Secondary: #8b5cf6 (Purple)
- Accent: #06b6d4 (<PERSON><PERSON>)
- Dark: #0f172a (Slate)
- Light: #f8fafc (Slate)
- Text: #334155 (Slate)
******************************************/

/* CSS Variables */
:root {
    --primary: #6366f1;
    --secondary: #8b5cf6;
    --accent: #06b6d4;
    --dark: #0f172a;
    --light: #f8fafc;
    --text: #334155;
    --text-light: #64748b;
    --white: #ffffff;
    --gradient: linear-gradient(135deg, var(--primary), var(--secondary));
    --gradient-accent: linear-gradient(135deg, var(--accent), var(--primary));
    --shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 20px 40px rgba(0, 0, 0, 0.15);
    --border-radius: 12px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    line-height: 1.6;
    color: var(--text);
    background-color: var(--light);
    overflow-x: hidden;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-family: 'Space Grotesk', sans-serif;
    font-weight: 600;
    line-height: 1.2;
    color: var(--dark);
}

h1 {
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 700;
}

h2 {
    font-size: clamp(2rem, 4vw, 3rem);
}

h3 {
    font-size: clamp(1.5rem, 3vw, 2rem);
}

h4 {
    font-size: clamp(1.25rem, 2.5vw, 1.5rem);
}

p {
    font-size: 1.1rem;
    line-height: 1.7;
    color: var(--text-light);
}

a {
    text-decoration: none;
    color: inherit;
    transition: var(--transition);
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

/* Gradient Text */
.gradient-text {
    background: var(--gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.title-accent {
    color: var(--primary);
}

/* Section Styles */
section {
    padding: 6rem 0;
    position: relative;
}

.section-title {
    text-align: center;
    margin-bottom: 1rem;
}

.section-subtitle {
    text-align: center;
    max-width: 600px;
    margin: 0 auto 3rem;
    font-size: 1.2rem;
}

/* Buttons */
.btn-primary {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 2rem;
    background: var(--gradient);
    color: var(--white);
    border: none;
    border-radius: var(--border-radius);
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    transition: var(--transition);
    box-shadow: var(--shadow);
    text-decoration: none;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    color: var(--white);
}

.btn-secondary {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 2rem;
    background: transparent;
    color: var(--text);
    border: 2px solid var(--primary);
    border-radius: var(--border-radius);
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    transition: var(--transition);
    text-decoration: none;
}

.btn-secondary:hover {
    background: var(--primary);
    color: var(--white);
    transform: translateY(-2px);
}

/* Particle Background */
#particles-js {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: rgba(15, 23, 42, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    transition: var(--transition);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 1rem 2rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.logo-text {
    font-family: 'Space Grotesk', sans-serif;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--white);
}

.logo-dot {
    width: 8px;
    height: 8px;
    background: var(--gradient);
    border-radius: 50%;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.2); opacity: 0.8; }
}

.nav-menu {
    display: flex;
    gap: 2rem;
    list-style: none;
}

.nav-link {
    color: var(--white);
    font-weight: 500;
    position: relative;
    transition: var(--transition);
}

.nav-link:hover {
    color: var(--accent);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--gradient);
    transition: var(--transition);
}

.nav-link:hover::after {
    width: 100%;
}

.nav-social {
    display: flex;
    gap: 1rem;
}

.social-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    color: var(--white);
    transition: var(--transition);
}

.social-link:hover {
    background: var(--gradient);
    transform: translateY(-2px);
    color: var(--white);
}

.hamburger {
    display: none;
    background: none;
    border: none;
    cursor: pointer;
    padding: 0;
    z-index: 1000;
}

.hamburger .bar {
    display: block;
    width: 25px;
    height: 3px;
    margin: 5px 0;
    background-color: var(--dark);
    transition: all 0.3s ease;
}

@media screen and (max-width: 768px) {
    .hamburger {
        display: block;
    }

    .nav-menu {
        position: fixed;
        top: 70px;
        left: -100%;
        width: 100%;
        height: calc(100vh - 70px);
        background: var(--white);
        flex-direction: column;
        align-items: center;
        padding: 2rem 0;
        transition: 0.3s ease-in-out;
        z-index: 999;
    }

    .nav-menu.active {
        left: 0;
    }

    .hamburger.active .bar:nth-child(1) {
        transform: translateY(8px) rotate(45deg);
    }

    .hamburger.active .bar:nth-child(2) {
        opacity: 0;
    }

    .hamburger.active .bar:nth-child(3) {
        transform: translateY(-8px) rotate(-45deg);
    }

    .nav-social {
        display: none;
    }
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
    overflow: hidden;
}

.hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    width: 100%;
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    min-height: 80vh;
}

.hero-text {
    z-index: 2;
}

.hero-title {
    margin-bottom: 2rem;
    color: var(--white);
}

.title-line {
    display: block;
    animation: slideInUp 0.8s ease-out forwards;
    opacity: 0;
    transform: translateY(30px);
}

.title-line:nth-child(1) { animation-delay: 0.2s; }
.title-line:nth-child(2) { animation-delay: 0.4s; }
.title-line:nth-child(3) { animation-delay: 0.6s; }

@keyframes slideInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.hero-subtitle {
    font-size: 1.3rem;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 3rem;
    animation: fadeIn 1s ease-out 0.8s forwards;
    opacity: 0;
}

@keyframes fadeIn {
    to { opacity: 1; }
}

.hero-buttons {
    display: flex;
    gap: 1.5rem;
    flex-wrap: wrap;
    animation: fadeIn 1s ease-out 1s forwards;
    opacity: 0;
}

.hero-visual {
    position: relative;
    height: 500px;
}

.floating-elements {
    position: relative;
    width: 100%;
    height: 100%;
}

.floating-card {
    position: absolute;
    width: 80px;
    height: 80px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: 2rem;
    animation: float 6s ease-in-out infinite;
}

.card-1 {
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.card-2 {
    top: 50%;
    right: 20%;
    animation-delay: 2s;
}

.card-3 {
    bottom: 20%;
    left: 30%;
    animation-delay: 4s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(5deg); }
}

.scroll-indicator {
    position: absolute;
    bottom: 2rem;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.9rem;
    animation: bounce 2s infinite;
}

.scroll-line {
    width: 2px;
    height: 30px;
    background: var(--gradient);
    border-radius: 2px;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateX(-50%) translateY(0); }
    40% { transform: translateX(-50%) translateY(-10px); }
    60% { transform: translateX(-50%) translateY(-5px); }
}


/* About Section */
.about {
    background: var(--white);
    position: relative;
}

.about-header {
    text-align: center;
    margin-bottom: 4rem;
}

.about-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
}

.about-card {
    background: var(--white);
    padding: 2.5rem 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    text-align: center;
    position: relative;
    overflow: hidden;
    transition: var(--transition);
    border: 1px solid rgba(99, 102, 241, 0.1);
}

.about-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-lg);
}

.card-icon {
    width: 80px;
    height: 80px;
    background: var(--gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 2rem;
    color: var(--white);
    position: relative;
    z-index: 2;
}

.about-card h3 {
    margin-bottom: 1rem;
    color: var(--dark);
}

.about-card p {
    color: var(--text-light);
    line-height: 1.6;
}

.card-glow {
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(99, 102, 241, 0.1) 0%, transparent 70%);
    opacity: 0;
    transition: var(--transition);
    z-index: 1;
}

.about-card:hover .card-glow {
    opacity: 1;
}

/* Services Section */
.services {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    position: relative;
}

.services-header {
    text-align: center;
    margin-bottom: 4rem;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2.5rem;
}

.service-card {
    background: var(--white);
    padding: 3rem 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    position: relative;
    overflow: hidden;
    transition: var(--transition);
    border: 1px solid rgba(99, 102, 241, 0.1);
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-lg);
}

.service-icon {
    position: relative;
    width: 100px;
    height: 100px;
    margin-bottom: 2rem;
}

.service-icon i {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 3rem;
    color: var(--primary);
    z-index: 2;
}

.icon-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient);
    border-radius: 50%;
    opacity: 0.1;
    transition: var(--transition);
}

.service-card:hover .icon-bg {
    opacity: 0.2;
    transform: scale(1.1);
}

.service-card h3 {
    margin-bottom: 1rem;
    color: var(--dark);
}

.service-card p {
    color: var(--text-light);
    margin-bottom: 2rem;
    line-height: 1.6;
}

.service-features {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.service-features span {
    background: rgba(99, 102, 241, 0.1);
    color: var(--primary);
    padding: 0.3rem 0.8rem;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
}


/* Why Choose Us Section */
.why-choose-us {
    background: var(--white);
    position: relative;
}

.why-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.why-text {
    padding-right: 2rem;
}

.why-features {
    margin-top: 2rem;
}

.feature-item {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
    align-items: flex-start;
}

.feature-icon {
    width: 60px;
    height: 60px;
    background: var(--gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: 1.5rem;
    flex-shrink: 0;
}

.feature-content h4 {
    margin-bottom: 0.5rem;
    color: var(--dark);
}

.feature-content p {
    color: var(--text-light);
}

.why-visual {
    position: relative;
}

.tech-stack {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
}

.tech-item {
    background: var(--gradient);
    color: var(--white);
    padding: 1rem;
    border-radius: var(--border-radius);
    text-align: center;
    font-weight: 600;
    animation: techFloat 3s ease-in-out infinite;
}

.tech-item:nth-child(1) { animation-delay: 0s; }
.tech-item:nth-child(2) { animation-delay: 0.5s; }
.tech-item:nth-child(3) { animation-delay: 1s; }
.tech-item:nth-child(4) { animation-delay: 1.5s; }
.tech-item:nth-child(5) { animation-delay: 2s; }
.tech-item:nth-child(6) { animation-delay: 2.5s; }

@keyframes techFloat {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}


/* Projects Section */
.projects {
    background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
    color: var(--white);
    position: relative;
}

.projects-header {
    text-align: center;
    margin-bottom: 4rem;
}

.projects-header .section-title,
.projects-header .section-subtitle {
    color: var(--white);
}

.projects-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.project-card {
    position: relative;
    border-radius: var(--border-radius);
    overflow: hidden;
    transition: var(--transition);
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.project-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.project-image {
    position: relative;
    overflow: hidden;
    height: 250px;
}

.project-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.project-card:hover .project-image img {
    transform: scale(1.1);
}

.project-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(99, 102, 241, 0.9), rgba(139, 92, 246, 0.9));
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: var(--transition);
}

.project-card:hover .project-overlay {
    opacity: 1;
}

.project-info {
    text-align: center;
    color: var(--white);
    padding: 2rem;
}

.project-info h3 {
    color: var(--white);
    margin-bottom: 1rem;
}

.project-info p {
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 1.5rem;
}

.project-tech {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    justify-content: center;
}

.project-tech span {
    background: rgba(255, 255, 255, 0.2);
    color: var(--white);
    padding: 0.3rem 0.8rem;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
}


/* Contact Section */
.contact {
    background: var(--white);
    position: relative;
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: start;
}

.contact-info {
    padding-right: 2rem;
}

.contact-details {
    margin-top: 3rem;
}

.contact-item {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
    align-items: center;
}

.contact-icon {
    width: 60px;
    height: 60px;
    background: var(--gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: 1.5rem;
    flex-shrink: 0;
}

.contact-text h4 {
    margin-bottom: 0.5rem;
    color: var(--dark);
}

.contact-text p {
    color: var(--text-light);
}

.contact-form {
    background: rgba(99, 102, 241, 0.05);
    padding: 3rem;
    border-radius: var(--border-radius);
    border: 1px solid rgba(99, 102, 241, 0.1);
}

.form-group {
    position: relative;
    margin-bottom: 2rem;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 1rem;
    border: 2px solid rgba(99, 102, 241, 0.2);
    border-radius: var(--border-radius);
    background: var(--white);
    font-size: 1rem;
    transition: var(--transition);
    outline: none;
}

.form-group input:focus,
.form-group textarea:focus {
    border-color: var(--primary);
}

.form-group label {
    position: absolute;
    top: 1rem;
    left: 1rem;
    color: var(--text-light);
    transition: var(--transition);
    pointer-events: none;
    background: var(--white);
    padding: 0 0.5rem;
}

.form-group input:focus + label,
.form-group input:not(:placeholder-shown) + label,
.form-group textarea:focus + label,
.form-group textarea:not(:placeholder-shown) + label {
    top: -0.5rem;
    left: 0.5rem;
    font-size: 0.85rem;
    color: var(--primary);
}

.form-group textarea {
    height: 120px;
    resize: vertical;
}


/* Footer */
.footer {
    background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
    color: var(--white);
    padding: 4rem 0 2rem;
}

.footer-content {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 3rem;
    margin-bottom: 3rem;
}

.footer-brand h3 {
    color: var(--white);
    margin-bottom: 1rem;
    font-size: 2rem;
}

.footer-brand p {
    color: rgba(255, 255, 255, 0.7);
}

.footer-links {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
}

.footer-section h4 {
    color: var(--white);
    margin-bottom: 1rem;
    font-size: 1.2rem;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul li a {
    color: rgba(255, 255, 255, 0.7);
    transition: var(--transition);
}

.footer-section ul li a:hover {
    color: var(--accent);
}

.footer-social {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
}

.footer-social .social-link {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    transition: var(--transition);
}

.footer-social .social-link:hover {
    background: var(--gradient);
    transform: translateY(-2px);
}

.footer-section p {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-bottom p {
    color: rgba(255, 255, 255, 0.5);
    font-size: 0.9rem;
}


/* AOS Animation Overrides */
[data-aos] {
    opacity: 0;
    transition-property: opacity, transform;
}

[data-aos].aos-animate {
    opacity: 1;
}

[data-aos="fade-up"] {
    transform: translateY(30px);
}

[data-aos="fade-up"].aos-animate {
    transform: translateY(0);
}

[data-aos="fade-right"] {
    transform: translateX(-30px);
}

[data-aos="fade-right"].aos-animate {
    transform: translateX(0);
}

[data-aos="fade-left"] {
    transform: translateX(30px);
}

[data-aos="fade-left"].aos-animate {
    transform: translateX(0);
}

[data-aos="zoom-in"] {
    transform: scale(0.8);
}

[data-aos="zoom-in"].aos-animate {
    transform: scale(1);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .container {
        padding: 0 1.5rem;
    }

    .hero-content {
        grid-template-columns: 1fr;
        gap: 3rem;
        text-align: center;
    }

    .why-content {
        grid-template-columns: 1fr;
        gap: 3rem;
    }

    .contact-content {
        grid-template-columns: 1fr;
        gap: 3rem;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .footer-links {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        top: 70px;
        left: -100%;
        width: 100%;
        height: calc(100vh - 70px);
        background: rgba(15, 23, 42, 0.98);
        backdrop-filter: blur(10px);
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        padding-top: 2rem;
        transition: var(--transition);
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-social {
        display: none;
    }

    .hamburger {
        display: flex;
    }

    .hamburger.active span:nth-child(1) {
        transform: rotate(45deg) translate(5px, 5px);
    }

    .hamburger.active span:nth-child(2) {
        opacity: 0;
    }

    .hamburger.active span:nth-child(3) {
        transform: rotate(-45deg) translate(7px, -6px);
    }

    section {
        padding: 4rem 0;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .about-grid {
        grid-template-columns: 1fr;
    }

    .services-grid {
        grid-template-columns: 1fr;
    }

    .projects-grid {
        grid-template-columns: 1fr;
    }

    .tech-stack {
        grid-template-columns: repeat(2, 1fr);
    }

    .footer-links {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .contact-form {
        padding: 2rem;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 1rem;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .btn-primary,
    .btn-secondary {
        padding: 0.8rem 1.5rem;
        font-size: 0.9rem;
    }

    .about-card,
    .service-card {
        padding: 2rem 1.5rem;
    }

    .floating-card {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }
}

/* Smooth scrolling for all browsers */
html {
    scroll-behavior: smooth;
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--light);
}

::-webkit-scrollbar-thumb {
    background: var(--gradient);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary);
}


/*****************/
/*     Works     */
/*****************/
.basic-4 {
	padding-bottom: 1rem;
}

.basic-4 .text-container {
	margin-bottom: 4rem;
}

.basic-4 .image-container {
	margin-bottom: 1rem;
}

/* Hover Animation */
.basic-4 .image-container {
	overflow: hidden;
	margin-right: auto;
	margin-left: auto;
}

.basic-4 .image-container img {
	margin: 0;
	transition: all 0.2s;
}

.basic-4 .image-container:hover img {
	-moz-transform: scale(1.15);
	-webkit-transform: scale(1.15);
	transform: scale(1.15);
} 
/* end of hover animation */


/************************/
/*     Testimonials     */
/************************/
.cards-1 {
	padding-top: 3rem;
	padding-bottom: 3.875rem;
}

.cards-1 .h2-heading {
	margin-bottom: 3rem;
	text-align: center;
}

.cards-1 .card {
	margin-bottom: 4rem;
	border: none;
}

.cards-1 .card-body {
	padding: 0;
	text-align: center;
}

.cards-1 .testimonial-text {
	margin-bottom: 1.625rem;
}

.cards-1 img {
	display: inline-block;
	width: 60px;
	margin-bottom: 0.875rem;
	border-radius: 50%;
}

.cards-1 .text {
	margin-top: 0.375rem;
}

.cards-1 .testimonial-author {
	color: #24262a;
}

.cards-1 .testimonial-author,
.cards-1 .occupation {
	font-size: 0.875rem;
	line-height: 1.375rem;
}


/*********************/
/*     Questions     */
/*********************/
.accordion-1 {
	padding-top: 7.75rem;
	padding-bottom: 6.75rem;
}

.accordion-1 .h2-heading {
	margin-bottom: 2.5rem;
	text-align: center;
}

.accordion-1 .card {
	margin-bottom: 1.75rem;
	border: none;
}

.accordion-1 .card-header {
	padding: 0;
	border: none;
	background-color: transparent;
}

.accordion-1 .card button {
	padding: 0;
	color: #24262a;
	font-weight: 600;
	font-size: 1.125rem;
	line-height: 1.75rem;
	font-family: 'Poppins';
}

.accordion-1 .card button:hover {
	text-decoration: none;
}

.accordion-1 .card button:before {
	float: left;
	margin-right: 0.625rem;
	font-family: 'Font Awesome 5 Free';
	font-weight: 900;
	content: "\f078";
	transition: all 0.2s;
}

.accordion-1 .card button.collapsed:before {
	-webkit-transform: rotate(180deg);
	-moz-transform: rotate(180deg);
	transform: rotate(180deg);
} 

.accordion-1 .card-body {
	padding: 0.75rem 1.5rem 0.5rem 1.5rem;
}


/*******************/
/*     Contact     */
/*******************/
.form-1 {
	padding-top: 7.75rem;
	padding-bottom: 7rem;
	text-align: center;
}

.form-1 .h2-heading {
	margin-bottom: 0.75rem;
	text-align: center;
}

.form-1 .p-heading {
	margin-bottom: 4rem;
}


/******************/
/*     Footer     */
/******************/
.footer {
	padding-top: 6.25rem;
	padding-bottom: 2rem;
	background-color: #24262a;
	text-align: center;
}

.footer a {
	text-decoration: none;
}

.footer .fa-stack {
	width: 2em;
	margin-bottom: 1.25rem;
	margin-right: 0.375rem;
	font-size: 1.5rem;
}

.footer .fa-stack .fa-stack-1x {
    color: #ffffff;
	transition: all 0.2s ease;
}

.footer .fa-stack .fa-stack-2x {
	color: #0b36a8;
	transition: all 0.2s ease;
}

.footer .fa-stack:hover .fa-stack-1x {
	color: #0b36a8;
}

.footer .fa-stack:hover .fa-stack-2x {
    color: #ffffff;
}


/*********************/
/*     Copyright     */
/*********************/
.copyright {
	padding-top: 1rem;
	padding-bottom: 1rem;
	background-color: #24262a;
	text-align: center;
}

.copyright .p-small {
	color: #9da6ac;
	text-align: center;
}

.copyright a {
	color: #9da6ac;
}


/******************************/
/*     Back To Top Button     */
/******************************/
a.back-to-top {
	position: fixed;
	z-index: 999;
	right: 12px;
	bottom: 12px;
	display: none;
	width: 42px;
	height: 42px;
	border-radius: 30px;
	background: #3a424d url("../images/up-arrow.png") no-repeat center 47%;
	background-size: 18px 18px;
	text-indent: -9999px;
}

a:hover.back-to-top {
	background-color: #0b36a8;
}


/***********************/
/*     Extra Pages     */
/***********************/
.ex-header {
	padding-top: 8.5rem;
	padding-bottom: 4rem;
	background-color: #24262a;
}

.ex-header h1 {
	color: #ffffff;
}

.ex-basic-1 .list-unstyled .fas {
	font-size: 0.375rem;
	line-height: 1.625rem;
}

.ex-basic-1 .list-unstyled .media-body {
	margin-left: 0.5rem;
}

.ex-basic-1 .text-box {
	padding: 1.25rem 1.25rem 0.5rem 1.25rem;
	background-color: #f7f9fb;
}

.ex-cards-1 .card {
	border: none;
	background-color: transparent;
}

.ex-cards-1 .card .fa-stack {
	width: 2em;
	font-size: 1.125rem;
}

.ex-cards-1 .card .fa-stack-2x {
	color: #0b36a8;
}

.ex-cards-1 .card .fa-stack-1x {
	color: #ffffff;
	font-weight: 700;
	line-height: 2.125rem;
}

.ex-cards-1 .card .list-unstyled .media-body {
	margin-left: 0.75rem;
}

.ex-cards-1 .card .list-unstyled .media-body h5 {
	margin-top: 0.125rem;
	margin-bottom: 0.375rem;
}


/*************************/
/*     Media Queries     */
/*************************/	
/* Min-width 768px */
@media (min-width: 768px) {

	/* Header */
	.header {
		padding-top: 11rem;
		padding-bottom: 9rem;
		
	}

	/* end of header */


	/* Services */
	.basic-2 .text-box {
		padding-right: 2.125rem;
		padding-left: 2.125rem;
	}
	/* end of services */


	/* Extra Pages */
	.ex-basic-1 .text-box {
		padding: 1.75rem 2rem 0.875rem 2rem;
	}
	/* end of extra pages */
}
/* end of min-width 768px */


/* Min-width 992px */
@media (min-width: 992px) {
	
	/* General Styles */
	.h2-heading {
		width: 35.25rem;
		margin-right: auto;
		margin-left: auto;
	}

	.p-heading {
		width: 46rem;
		margin-right: auto;
		margin-left: auto;
	}
	/* end of general styles */


	/* Navigation */
	.navbar {
		padding-top: 1.75rem;
		background-color: transparent;
		box-shadow: none;
		transition: all 0.2s;
	}

	.navbar.top-nav-collapse {
		padding-top: 0.5rem;
		padding-top: 0.5rem;
		background-color: #24262a;
		box-shadow: 0 3px 6px 0 rgba(0, 0, 0, 0.025);
	}

	.offcanvas-collapse {
		position: static;
		top: auto;
		bottom: auto;
		left: auto;
		width: auto;
		padding-right: 0;
		padding-left: 0;
		background-color: transparent;
		overflow-y: visible;
		visibility: visible;
	}

	.offcanvas-collapse.open {
		-webkit-transform: none;
		transform: none;
	}

	.navbar .navbar-nav {
		margin-top: 0;
		margin-bottom: 0;
	}
	
	.navbar .nav-item .nav-link {
		padding-right: 0.625rem;
		padding-left: 0.625rem;
	}

	.navbar .dropdown-menu {
		margin-top: 0.25rem;
		box-shadow: 0 3px 3px 1px rgba(0, 0, 0, 0.08);
	}

	.navbar .dropdown-divider {
		width: 90%;
	}

	.navbar .social-icons {
		margin-left: 0.5rem;
	}
	
	.navbar .fa-stack {
		margin-right: 0;
		margin-left: 0.25rem;
	}
	/* end of navigation */


	/* Header */
	.header {
		padding-top: 15rem;
		padding-bottom: 14rem;
		text-align: left;
	}
	/* end of header */


	/* Details */
	.split {
		height: 760px;
	}
	
	.split .area-1 {
		height: 100%;
		width: 50%;
		display: inline-block;
	}
	
	.split .area-2 {
		height: 100%;
		width: 50%;
		display: inline-block;
		vertical-align: top;
	}

	.split .text-container {
		width: 400px;
		margin-left: 3rem;
	}
	/* end of details */


	/* Testimonials */
	.cards-1 .card {
		display: inline-block;
		width: 290px;
		vertical-align: top;
	}

	.cards-1 .card:nth-of-type(3n+2) {
		margin-right: 1.5rem;
		margin-left: 1.5rem;
	}

	.cards-1 .card-body {
		text-align: left;
	}

	.cards-1 .details {
		display: flex;
		align-items: flex-start;
	}
	
	.cards-1 .details .text {
		flex-direction: column;
	}
	
	.cards-1 .details img {
		margin-right: 1.125rem;
	}
	/* end of testimonials */


	/* Accordion */
	.accordion-1 .accordion {
		width: 730px;
		margin-right: auto;
		margin-left: auto;
	}
	/* end of accordion */


	/* Contact */
	.form-1 form {
		width: 790px;
		margin-right: auto;
		margin-left: auto;
	}
	/* end of contact */


	/* Extra Pages */
	.ex-cards-1 .card {
		display: inline-block;
		width: 296px;
		vertical-align: top;
	}

	.ex-cards-1 .card:nth-of-type(3n+2) {
		margin-right: 1rem;
		margin-left: 1rem;
	}
	/* end of extra pages */
}
/* end of min-width 992px */


/* Min-width 1200px */
@media (min-width: 1200px) {

	/* General Styles */
	.h1-large {
		font-size: 3.75rem;
		line-height: 4.625rem;
	}
	/* end of general styles */


	/* Navigation */
	.navbar .container {
		max-width: 1330px;
	}
	/* end of navigation */


	/* Header */
	.header {
		padding-top: 18.25rem;
		padding-bottom: 17.25rem;
	}
	/* end of header */


	/* About */
	.basic-1 .text-container.first {
		margin-right: 2rem;
	}

	.basic-1 .text-container.second {
		margin-right: 1rem;
		margin-left: 1rem;
	}

	.basic-1 .text-container.third {
		margin-left: 2rem;
	}
	/* end of about */


	/* Details */
	.split {
		height: 700px;
	}

	.split .text-container {
		width: 460px;
		margin-left: 5rem;
	}
	/* end of details */


	/* Testimonials */
	.cards-1 .card {
		width: 338px;
	}
	
	.cards-1 .card:nth-of-type(3n+2) {
		margin-right: 2.625rem;
		margin-left: 2.625rem;
	}
	/* end of testimonials */
	

	/* Extra Pages */
	.ex-cards-1 .card {
		width: 336px;
	}

	.ex-cards-1 .card:nth-of-type(3n+2) {
		margin-right: 2.875rem;
		margin-left: 2.875rem;
	}
	/* end of extra pages */
}
/* end of min-width 1200px */